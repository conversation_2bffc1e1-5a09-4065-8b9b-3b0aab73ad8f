#!/usr/bin/env python3
"""
Debug script to investigate specific timeline rendering issues.
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from mermaid_render import Me<PERSON><PERSON><PERSON><PERSON>, TimelineDiagram
from mermaid_render.validators import MermaidValidator

def debug_many_events_issue():
    """Debug the many events issue."""
    print("=== Debugging Many Events Issue ===")
    
    timeline = TimelineDiagram(title="Many Events Timeline")
    
    # Add many events to test performance and rendering limits
    for year in range(2000, 2025):
        for quarter in range(1, 5):
            timeline.add_event(f"{year}", f"Q{quarter} Event for {year}")
    
    mermaid_code = timeline.to_mermaid()
    print(f"Generated Mermaid code (first 500 chars):\n{mermaid_code[:500]}...")
    print(f"Total length: {len(mermaid_code)} characters")
    
    # Validate the generated code
    validator = MermaidValidator()
    result = validator.validate(mermaid_code)
    
    print(f"\nValidation result: {result.is_valid}")
    if not result.is_valid:
        print("Errors:")
        for error in result.errors:
            print(f"  - {error}")
        print("Warnings:")
        for warning in result.warnings:
            print(f"  - {warning}")
    
    # Try to save the code to a file for inspection
    with open("debug_many_events.mmd", "w") as f:
        f.write(mermaid_code)
    print("Saved generated code to debug_many_events.mmd")

def debug_duplicate_periods_issue():
    """Debug the duplicate periods issue."""
    print("\n=== Debugging Duplicate Periods Issue ===")
    
    timeline = TimelineDiagram(title="Duplicate Periods Timeline")
    
    # Add duplicate periods in different sections
    section1 = timeline.add_section("Section 1")
    section1.add_period("2020").add_event("Event in Section 1")
    
    section2 = timeline.add_section("Section 2")
    section2.add_period("2020").add_event("Event in Section 2")
    
    # Also add standalone periods with same name
    timeline.add_event("2020", "Standalone event")
    
    mermaid_code = timeline.to_mermaid()
    print(f"Generated Mermaid code:\n{mermaid_code}")
    
    # Validate the generated code
    validator = MermaidValidator()
    result = validator.validate(mermaid_code)
    
    print(f"\nValidation result: {result.is_valid}")
    if not result.is_valid:
        print("Errors:")
        for error in result.errors:
            print(f"  - {error}")
        print("Warnings:")
        for warning in result.warnings:
            print(f"  - {warning}")
    
    # Try to save the code to a file for inspection
    with open("debug_duplicate_periods.mmd", "w") as f:
        f.write(mermaid_code)
    print("Saved generated code to debug_duplicate_periods.mmd")

def test_simpler_many_events():
    """Test with fewer events to find the threshold."""
    print("\n=== Testing Simpler Many Events ===")
    
    renderer = MermaidRenderer()
    
    # Test with different numbers of events
    for num_years in [5, 10, 15, 20, 25]:
        print(f"\nTesting with {num_years} years...")
        
        timeline = TimelineDiagram(title=f"Timeline with {num_years} years")
        
        for year in range(2000, 2000 + num_years):
            timeline.add_event(f"{year}", f"Event for {year}")
        
        try:
            svg_content = renderer.render(timeline, format="svg")
            print(f"✅ {num_years} years successful! SVG length: {len(svg_content)}")
        except Exception as e:
            print(f"❌ {num_years} years failed: {e}")
            break

def test_correct_duplicate_handling():
    """Test the correct way to handle duplicate periods."""
    print("\n=== Testing Correct Duplicate Handling ===")
    
    timeline = TimelineDiagram(title="Correct Duplicate Handling")
    
    # Method 1: Add multiple events to the same period
    timeline.add_event("2020", "First event")
    timeline.add_event("2020", "Second event")  # Should add to existing period
    
    # Method 2: Use sections with different periods
    section1 = timeline.add_section("Phase 1")
    section1.add_period("Q1 2021").add_event("Phase 1 event")
    
    section2 = timeline.add_section("Phase 2")
    section2.add_period("Q2 2021").add_event("Phase 2 event")
    
    mermaid_code = timeline.to_mermaid()
    print(f"Generated Mermaid code:\n{mermaid_code}")
    
    renderer = MermaidRenderer()
    try:
        svg_content = renderer.render(timeline, format="svg")
        print(f"✅ Correct duplicate handling successful! SVG length: {len(svg_content)}")
        return True
    except Exception as e:
        print(f"❌ Correct duplicate handling failed: {e}")
        return False

if __name__ == "__main__":
    debug_many_events_issue()
    debug_duplicate_periods_issue()
    test_simpler_many_events()
    test_correct_duplicate_handling()
