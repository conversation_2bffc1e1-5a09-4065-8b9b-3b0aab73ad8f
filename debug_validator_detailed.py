#!/usr/bin/env python3
"""
Detailed debug script to test the validator logic.
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from mermaid_render.validators import MermaidValidator

def debug_validator_logic():
    """Debug the exact validator logic."""
    print("=== Debugging Validator Logic ===")
    
    # Create the exact line that's causing issues
    line = "              : Second event"
    
    print(f"Testing line: '{line}'")
    print(f"Line length: {len(line)}")
    print(f"Line repr: {repr(line)}")
    
    # Check the exact condition
    parts = line.split(":", 1)
    period = parts[0].strip()
    event = parts[1].strip()
    
    print(f"Period: '{period}' (repr: {repr(period)})")
    print(f"Event: '{event}' (repr: {repr(event)})")
    print(f"not period: {not period}")
    print(f"line.startswith('              :'): {line.startswith('              :')}")
    print(f"not line.startswith('              :'): {not line.startswith('              :')}")
    
    condition = not period and not line.startswith("              :")
    print(f"Final condition (not period and not line.startswith('              :')): {condition}")
    
    if condition:
        print("ERROR WOULD BE ADDED")
    else:
        print("ERROR WOULD NOT BE ADDED")

def test_with_actual_validator():
    """Test with the actual validator to see what happens."""
    print("\n=== Testing with Actual Validator ===")
    
    # Create a minimal timeline that should work
    timeline_code = """timeline
    2020 : First event
              : Second event"""
    
    print(f"Testing timeline:\n{timeline_code}")
    
    validator = MermaidValidator()
    
    # Let's manually step through the validation process
    lines = timeline_code.strip().split("\n")
    print(f"\nLines: {lines}")
    
    for i, line in enumerate(lines, 1):
        line = line.strip()
        print(f"\nProcessing line {i}: '{line}' (stripped)")
        
        if ":" in line:
            parts = line.split(":", 1)
            period = parts[0].strip()
            event = parts[1].strip()
            
            print(f"  Period: '{period}' (empty: {not period})")
            print(f"  Event: '{event}' (empty: {not event})")
            print(f"  Original line starts with '              :': {lines[i-1].startswith('              :')}")
            
            # Check the condition using the original line (not stripped)
            original_line = lines[i-1]
            condition = not period and not original_line.startswith("              :")
            print(f"  Condition result: {condition}")
            
            if condition:
                print(f"  -> ERROR: Empty time period")

if __name__ == "__main__":
    debug_validator_logic()
    test_with_actual_validator()
