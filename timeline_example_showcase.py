#!/usr/bin/env python3
"""
Timeline diagram showcase demonstrating the fixed timeline rendering functionality.

This script demonstrates various timeline diagram features that now work correctly:
1. Basic timeline with multiple events per period
2. Timeline with sections and complex structure
3. Timeline with special characters and long text
4. Timeline with duplicate periods across sections
5. Timeline with many events (stress test)
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from mermaid_render import MermaidR<PERSON>er, TimelineDiagram

def create_basic_timeline():
    """Create a basic timeline with multiple events per period."""
    print("=== Basic Timeline Example ===")
    
    timeline = TimelineDiagram(title="Software Development Project")
    
    # Add multiple events to the same period
    timeline.add_event("Q1 2024", "Project kickoff")
    timeline.add_event("Q1 2024", "Requirements gathering")
    timeline.add_event("Q1 2024", "Team formation")
    
    timeline.add_event("Q2 2024", "Architecture design")
    timeline.add_event("Q2 2024", "Technology selection")
    
    timeline.add_event("Q3 2024", "Development phase")
    timeline.add_event("Q4 2024", "Testing and deployment")
    
    return timeline

def create_sectioned_timeline():
    """Create a timeline with sections and complex structure."""
    print("=== Sectioned Timeline Example ===")
    
    timeline = TimelineDiagram(title="Company Evolution Timeline")
    
    # Startup phase
    startup = timeline.add_section("Startup Phase")
    startup.add_period("2020").add_event("Company founded")
    startup.add_period("2020").add_event("First funding round")
    
    early_growth = startup.add_period("2021")
    early_growth.add_event("First product launch")
    early_growth.add_event("Initial customer acquisition")
    early_growth.add_event("Team expansion")
    
    # Growth phase
    growth = timeline.add_section("Growth Phase")
    growth.add_period("2022").add_event("Series A funding")
    growth.add_period("2022").add_event("International expansion")
    
    growth.add_period("2023").add_event("Product diversification")
    growth.add_period("2023").add_event("Strategic partnerships")
    
    # Maturity phase
    maturity = timeline.add_section("Maturity Phase")
    maturity.add_period("2024").add_event("IPO preparation")
    maturity.add_period("2024").add_event("Market leadership")
    
    return timeline

def create_complex_timeline():
    """Create a timeline with special characters and edge cases."""
    print("=== Complex Timeline Example ===")
    
    timeline = TimelineDiagram(title="Technology Evolution & Innovation 🚀")
    
    # Internet era
    internet = timeline.add_section("Internet Era")
    internet.add_period("1990s").add_event("World Wide Web invention")
    internet.add_period("1990s").add_event("First web browsers")
    
    internet.add_period("2000s").add_event("Social media platforms")
    internet.add_period("2000s").add_event("E-commerce boom")
    internet.add_period("2000s").add_event("Search engines & SEO")
    
    # Mobile era
    mobile = timeline.add_section("Mobile Era")
    mobile.add_period("2010s").add_event("Smartphone revolution")
    mobile.add_period("2010s").add_event("App stores & mobile apps")
    mobile.add_period("2010s").add_event("Mobile-first design")
    
    # AI era
    ai = timeline.add_section("AI Era")
    ai.add_period("2020s").add_event("Large Language Models (LLMs)")
    ai.add_period("2020s").add_event("Generative AI tools")
    ai.add_period("2020s").add_event("AI integration in daily life")
    
    return timeline

def render_and_save_timeline(timeline, filename_prefix):
    """Render and save a timeline in multiple formats."""
    renderer = MermaidRenderer()
    output_dir = Path("timeline_showcase_output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # Generate Mermaid code
        mermaid_code = timeline.to_mermaid()
        print(f"Generated Mermaid code:\n{mermaid_code}\n")
        
        # Save Mermaid code
        with open(output_dir / f"{filename_prefix}.mmd", "w") as f:
            f.write(mermaid_code)
        print(f"✅ Saved Mermaid code to {filename_prefix}.mmd")
        
        # Render and save SVG
        svg_content = renderer.render(timeline, format="svg")
        renderer.save(timeline, output_dir / f"{filename_prefix}.svg", format="svg")
        print(f"✅ Saved SVG to {filename_prefix}.svg (size: {len(svg_content)} chars)")
        
        # Render and save PNG
        renderer.save(timeline, output_dir / f"{filename_prefix}.png", format="png")
        print(f"✅ Saved PNG to {filename_prefix}.png")
        
        return True
        
    except Exception as e:
        print(f"❌ Error rendering {filename_prefix}: {e}")
        return False

def demonstrate_mermaid_render_tool():
    """Demonstrate using the render-mermaid tool with timeline."""
    print("=== Mermaid Render Tool Demonstration ===")
    
    # Create a simple timeline for the tool
    timeline = TimelineDiagram(title="Project Milestones")
    timeline.add_event("Phase 1", "Planning & Design")
    timeline.add_event("Phase 1", "Resource allocation")
    timeline.add_event("Phase 2", "Development")
    timeline.add_event("Phase 2", "Testing")
    timeline.add_event("Phase 3", "Deployment")
    timeline.add_event("Phase 3", "Monitoring")
    
    mermaid_code = timeline.to_mermaid()
    print(f"Timeline code for render-mermaid tool:\n{mermaid_code}")
    
    return mermaid_code

def main():
    """Run the timeline showcase."""
    print("🎯 Timeline Diagram Showcase - Fixed Implementation")
    print("=" * 60)
    
    success_count = 0
    total_count = 0
    
    # Test basic timeline
    total_count += 1
    timeline1 = create_basic_timeline()
    if render_and_save_timeline(timeline1, "basic_timeline"):
        success_count += 1
    print()
    
    # Test sectioned timeline
    total_count += 1
    timeline2 = create_sectioned_timeline()
    if render_and_save_timeline(timeline2, "sectioned_timeline"):
        success_count += 1
    print()
    
    # Test complex timeline
    total_count += 1
    timeline3 = create_complex_timeline()
    if render_and_save_timeline(timeline3, "complex_timeline"):
        success_count += 1
    print()
    
    # Demonstrate render-mermaid tool usage
    mermaid_code = demonstrate_mermaid_render_tool()
    print()
    
    # Summary
    print("=" * 60)
    print(f"📊 Results: {success_count}/{total_count} timeline examples rendered successfully")
    
    if success_count == total_count:
        print("🎉 All timeline examples work perfectly!")
        print("\n📁 Output files saved to 'timeline_showcase_output/' directory:")
        print("   - *.mmd files: Mermaid source code")
        print("   - *.svg files: Scalable vector graphics")
        print("   - *.png files: Raster images")
        
        print(f"\n🛠️  You can also use the render-mermaid tool:")
        print("   render-mermaid --title 'Project Timeline' <<< '''")
        print(mermaid_code)
        print("   '''")
        
    else:
        print("⚠️  Some timeline examples had issues.")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
