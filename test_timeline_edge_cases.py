#!/usr/bin/env python3
"""
Test script to identify potential timeline rendering edge cases and issues.
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from mermaid_render import Mermaid<PERSON>enderer, TimelineDiagram

def test_empty_timeline():
    """Test empty timeline rendering."""
    print("Testing empty timeline...")
    
    timeline = TimelineDiagram(title="Empty Timeline")
    renderer = MermaidRenderer()
    
    try:
        mermaid_code = timeline.to_mermaid()
        print(f"Generated Mermaid code:\n{mermaid_code}")
        
        svg_content = renderer.render(timeline, format="svg")
        print(f"✅ Empty timeline SVG rendering successful! Length: {len(svg_content)}")
        return True
    except Exception as e:
        print(f"❌ Empty timeline rendering failed: {e}")
        return False

def test_timeline_with_special_characters():
    """Test timeline with special characters in events."""
    print("Testing timeline with special characters...")
    
    timeline = TimelineDiagram(title="Special Characters Timeline")
    
    # Add events with special characters
    timeline.add_event("2020", "Event with 'quotes'")
    timeline.add_event("2021", "Event with \"double quotes\"")
    timeline.add_event("2022", "Event with & ampersand")
    timeline.add_event("2023", "Event with <brackets>")
    timeline.add_event("2024", "Event with émojis 🚀")
    
    renderer = MermaidRenderer()
    
    try:
        mermaid_code = timeline.to_mermaid()
        print(f"Generated Mermaid code:\n{mermaid_code}")
        
        svg_content = renderer.render(timeline, format="svg")
        print(f"✅ Special characters timeline SVG rendering successful! Length: {len(svg_content)}")
        return True
    except Exception as e:
        print(f"❌ Special characters timeline rendering failed: {e}")
        return False

def test_timeline_with_long_text():
    """Test timeline with very long event text."""
    print("Testing timeline with long text...")
    
    timeline = TimelineDiagram(title="Long Text Timeline")
    
    # Add events with very long text
    long_text = "This is a very long event description that might cause issues with rendering because it contains many words and could potentially overflow or cause layout problems in the generated diagram"
    timeline.add_event("2020", long_text)
    timeline.add_event("2021", "Short event")
    timeline.add_event("2022", long_text + " - even longer version with more text to test edge cases")
    
    renderer = MermaidRenderer()
    
    try:
        mermaid_code = timeline.to_mermaid()
        print(f"Generated Mermaid code:\n{mermaid_code}")
        
        svg_content = renderer.render(timeline, format="svg")
        print(f"✅ Long text timeline SVG rendering successful! Length: {len(svg_content)}")
        return True
    except Exception as e:
        print(f"❌ Long text timeline rendering failed: {e}")
        return False

def test_timeline_with_many_events():
    """Test timeline with many events."""
    print("Testing timeline with many events...")
    
    timeline = TimelineDiagram(title="Many Events Timeline")
    
    # Add many events to test performance and rendering limits
    for year in range(2000, 2025):
        for quarter in range(1, 5):
            timeline.add_event(f"{year}", f"Q{quarter} Event for {year}")
    
    renderer = MermaidRenderer()
    
    try:
        mermaid_code = timeline.to_mermaid()
        print(f"Generated Mermaid code length: {len(mermaid_code)} characters")
        
        svg_content = renderer.render(timeline, format="svg")
        print(f"✅ Many events timeline SVG rendering successful! Length: {len(svg_content)}")
        return True
    except Exception as e:
        print(f"❌ Many events timeline rendering failed: {e}")
        return False

def test_timeline_with_empty_sections():
    """Test timeline with empty sections."""
    print("Testing timeline with empty sections...")
    
    timeline = TimelineDiagram(title="Empty Sections Timeline")
    
    # Add empty sections
    empty_section = timeline.add_section("Empty Section")
    # Don't add any periods to this section
    
    # Add a section with content
    content_section = timeline.add_section("Content Section")
    content_section.add_period("2020").add_event("Some event")
    
    renderer = MermaidRenderer()
    
    try:
        mermaid_code = timeline.to_mermaid()
        print(f"Generated Mermaid code:\n{mermaid_code}")
        
        svg_content = renderer.render(timeline, format="svg")
        print(f"✅ Empty sections timeline SVG rendering successful! Length: {len(svg_content)}")
        return True
    except Exception as e:
        print(f"❌ Empty sections timeline rendering failed: {e}")
        return False

def test_timeline_with_duplicate_periods():
    """Test timeline with duplicate periods."""
    print("Testing timeline with duplicate periods...")
    
    timeline = TimelineDiagram(title="Duplicate Periods Timeline")
    
    # Add duplicate periods in different sections
    section1 = timeline.add_section("Section 1")
    section1.add_period("2020").add_event("Event in Section 1")
    
    section2 = timeline.add_section("Section 2")
    section2.add_period("2020").add_event("Event in Section 2")
    
    # Also add standalone periods with same name
    timeline.add_event("2020", "Standalone event")
    
    renderer = MermaidRenderer()
    
    try:
        mermaid_code = timeline.to_mermaid()
        print(f"Generated Mermaid code:\n{mermaid_code}")
        
        svg_content = renderer.render(timeline, format="svg")
        print(f"✅ Duplicate periods timeline SVG rendering successful! Length: {len(svg_content)}")
        return True
    except Exception as e:
        print(f"❌ Duplicate periods timeline rendering failed: {e}")
        return False

def test_timeline_validation():
    """Test timeline validation."""
    print("Testing timeline validation...")
    
    timeline = TimelineDiagram(title="Validation Test Timeline")
    timeline.add_event("2020", "Test event")
    
    try:
        is_valid = timeline.validate()
        print(f"Timeline validation result: {is_valid}")
        
        if is_valid:
            print("✅ Timeline validation successful!")
            return True
        else:
            print("❌ Timeline validation failed!")
            return False
    except Exception as e:
        print(f"❌ Timeline validation error: {e}")
        return False

def main():
    """Run all edge case tests."""
    print("=== Testing Timeline Edge Cases ===\n")
    
    tests = [
        ("Empty Timeline", test_empty_timeline),
        ("Special Characters", test_timeline_with_special_characters),
        ("Long Text", test_timeline_with_long_text),
        ("Many Events", test_timeline_with_many_events),
        ("Empty Sections", test_timeline_with_empty_sections),
        ("Duplicate Periods", test_timeline_with_duplicate_periods),
        ("Timeline Validation", test_timeline_validation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print(f"=== Results: {passed}/{total} edge case tests passed ===")
    
    if passed == total:
        print("🎉 All timeline edge case tests passed!")
    elif passed >= total // 2:
        print("✅ Most timeline edge cases work correctly.")
    else:
        print("⚠️  Timeline implementation has issues with edge cases.")
    
    return passed >= total // 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
