#!/usr/bin/env python3
"""
Debug script to test the validator specifically.
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from mermaid_render.validators import MermaidValidator

def test_simple_timeline():
    """Test simple timeline validation."""
    print("=== Testing Simple Timeline Validation ===")
    
    # Test a simple timeline with multiple events
    timeline_code = """timeline
    title Test Timeline
    2020 : First event
              : Second event"""
    
    print(f"Testing timeline code:\n{timeline_code}")
    
    validator = MermaidValidator()
    result = validator.validate(timeline_code)
    
    print(f"\nValidation result: {result.is_valid}")
    if not result.is_valid:
        print("Errors:")
        for error in result.errors:
            print(f"  - {error}")
        print("Warnings:")
        for warning in result.warnings:
            print(f"  - {warning}")
    
    # Let's also check line by line
    lines = timeline_code.strip().split('\n')
    print(f"\nLine by line analysis:")
    for i, line in enumerate(lines, 1):
        print(f"Line {i}: '{line}'")
        print(f"  - Starts with '              :': {line.startswith('              :')}")
        print(f"  - Contains ':': {':' in line}")
        if ':' in line:
            parts = line.split(':', 1)
            period = parts[0].strip()
            event = parts[1].strip()
            print(f"  - Period: '{period}' (empty: {not period})")
            print(f"  - Event: '{event}' (empty: {not event})")

def test_exact_indentation():
    """Test exact indentation patterns."""
    print("\n=== Testing Exact Indentation Patterns ===")
    
    test_lines = [
        "              : Second event",  # 14 spaces
        "          : Second event",      # 10 spaces
        "    : Second event",            # 4 spaces
        ": Second event",                # 0 spaces
    ]
    
    for line in test_lines:
        print(f"\nTesting line: '{line}'")
        print(f"  - Starts with '              :': {line.startswith('              :')}")
        print(f"  - Starts with '          :': {line.startswith('          :')}")
        
        if ':' in line:
            parts = line.split(':', 1)
            period = parts[0].strip()
            event = parts[1].strip()
            print(f"  - Period: '{period}' (empty: {not period})")
            print(f"  - Event: '{event}' (empty: {not event})")

if __name__ == "__main__":
    test_simple_timeline()
    test_exact_indentation()
